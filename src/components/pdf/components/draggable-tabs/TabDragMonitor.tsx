import {usePdfStore} from '@/store/pdf-store';
import React, {useEffect, useRef, useCallback, useMemo} from 'react';
import {useDragLayer} from 'react-dnd';
import {DragItem, DragTypes} from './types';
import useDragTab from "@/components/pdf/components/draggable-tabs/hooks/drag-tab.ts";
import {CloseOutlined} from "@ant-design/icons";

export const TabDragMonitor = ({}) => {
    const createdWindowIdRef = useRef<string | null>(null);
    const processedDragHoverRef = useRef<string | null>(null);

    const {
        createWindow,
        updateWindow,
        closeWindow,
        removeTabFromWindow,
        setDragTabWindowId,
        setDragTabSourceWindowId,
        setDragHoverWindowId,
        setTabItems,
        dragHoverWindowId,
        windows
    } = usePdfStore((state) => ({
        createWindow: state.createWindow,
        updateWindow: state.updateWindow,
        closeWindow: state.closeWindow,
        removeTabFromWindow: state.removeTabFromWindow,
        setDragTabWindowId: state.setDragTabWindowId,
        setDragTabSourceWindowId: state.setDragTabSourceWindowId,
        setDragHoverWindowId: state.setDragHoverWindowId,
        setTabItems: state.setTabItems,
        dragHoverWindowId: state.dragHoverWindowId,
        windows: state.windows
    }));

    const {
        isDragging,
        item,
        currentOffset,
    } = useDragLayer((monitor) => ({
        isDragging: monitor.isDragging(),
        item: monitor.getItem() as DragItem,
        currentOffset: monitor.getSourceClientOffset(),
    }));

    const {checkIfOutsideContainer, getTabWindowId} = useDragTab();

    // 使用 useMemo 缓存计算结果，避免每次渲染都重新计算
    const dragInfo = useMemo(() => {
        if (!item) return null;

        const sourceWindowId = item?.windowId;
        const sourceWindow = windows.get(sourceWindowId);
        const isFromMainTabBar = sourceWindowId === 'main';
        const isSingleTabWindow = sourceWindow && sourceWindow.tabs.length === 1;
        const shouldHideWindow = !isFromMainTabBar && isSingleTabWindow;
        const isOutsideTabBar = checkIfOutsideContainer(item, currentOffset);

        return {
            sourceWindowId,
            sourceWindow,
            isFromMainTabBar,
            isSingleTabWindow,
            shouldHideWindow,
            isOutsideTabBar
        };
    }, [item, windows, checkIfOutsideContainer, currentOffset]);

    console.log("TabDragMonitor shouldHideWindow isOutsideTabBar", dragInfo?.shouldHideWindow, dragInfo?.isOutsideTabBar);

    // 处理拖拽窗口的创建和位置更新
    useEffect(() => {
        if (!isDragging || !currentOffset || !item || item.type !== DragTypes.TAB || !dragInfo) {
            createdWindowIdRef.current = null;
            processedDragHoverRef.current = null;
            setDragTabWindowId("")
            setDragTabSourceWindowId("")
            setDragHoverWindowId("")
            return;
        }

        const windowPosition = {
            x: currentOffset.x,
            y: currentOffset.y + 49
        };

        const {isOutsideTabBar, shouldHideWindow, sourceWindowId} = dragInfo;

        if ((isOutsideTabBar || shouldHideWindow) && !createdWindowIdRef.current) {
            const windowId = createWindow([item.tabItem], windowPosition);
            console.log('Created window for dragged tab:', windowId);
            createdWindowIdRef.current = windowId;
            setDragTabWindowId(windowId);
            setDragTabSourceWindowId(sourceWindowId);
        }

        if (createdWindowIdRef.current) {
            updateWindow(createdWindowIdRef.current!, {position: windowPosition, zIndex: Date.now()});
        }
    }, [isDragging, item?.id, currentOffset?.x, currentOffset?.y, dragInfo?.isOutsideTabBar, dragInfo?.shouldHideWindow]);

    // 处理拖拽悬浮时的标签移除逻辑
    useEffect(() => {
        if (!isDragging || !item || !dragInfo || !dragHoverWindowId) {
            processedDragHoverRef.current = null;
            return;
        }

        const {isFromMainTabBar, sourceWindowId} = dragInfo;

        // 避免重复处理同一个 dragHoverWindowId
        if (processedDragHoverRef.current === dragHoverWindowId) {
            return;
        }

        const dragHoverContainer = usePdfStore.getState().windowsContainer.get(dragHoverWindowId)?.current?.getBoundingClientRect();
        const shouldShowTab = checkIfOutsideContainer(item, currentOffset, dragHoverContainer);

        if (shouldShowTab && dragHoverWindowId !== sourceWindowId) {
            processedDragHoverRef.current = dragHoverWindowId;

            if (isFromMainTabBar) {
                const tabs = usePdfStore.getState().tabItems;
                const newTabs = tabs.filter(tab => tab.key !== item.id);
                setTabItems(newTabs);
            } else {
                removeTabFromWindow(dragHoverWindowId, item.id);
            }
        }
    }, [isDragging, item?.id, dragHoverWindowId, dragInfo?.isFromMainTabBar, dragInfo?.sourceWindowId]);

    // 计算是否应该显示拖拽标签
    const shouldShowTab = useMemo(() => {
        if (!isDragging || !currentOffset || !item || item.type !== DragTypes.TAB || !dragInfo) {
            return false;
        }

        const {isOutsideTabBar, shouldHideWindow} = dragInfo;
        let shouldShow = isOutsideTabBar || shouldHideWindow;

        if (dragHoverWindowId) {
            const dragHoverContainer = usePdfStore.getState().windowsContainer.get(dragHoverWindowId)?.current?.getBoundingClientRect();
            shouldShow = checkIfOutsideContainer(item, currentOffset, dragHoverContainer);
        }

        return shouldShow;
    }, [isDragging, currentOffset, item, dragInfo, dragHoverWindowId, checkIfOutsideContainer]);

    console.log("TabDragMonitor dragHoverWindowId shouldShowTab", dragHoverWindowId, shouldShowTab);

    if (!shouldShowTab) {
        return null;
    }
    return (
        <div className="bg-gray-50 border-b border border-gray-200 rounded-tl-lg rounded-tr-lg" style={{
            position: 'fixed',
            pointerEvents: 'none',
            zIndex: Date.now(),
            left: currentOffset.x,
            top: currentOffset.y,
            width: 800,
            height: '50px',
        }}>
            <div className="flex items-center min-w-0 flex-1">
                <div className="group">
                    <div className="
        group relative flex items-center px-3 py-2 cursor-pointer select-none
        border-r border-gray-200 min-w-0 max-w-48
        transition-all duration-200 ease-in-out
        bg-white border-b-2 border-b-blue-500 text-blue-600">
                        <div className="flex items-center min-w-0 flex-1"><span
                            className="truncate text-sm font-medium">{item.tabItem.label}</span></div>
                        <button className="
            ml-2 p-1 rounded-full opacity-0 group-hover:opacity-100
            hover:bg-gray-200 transition-opacity duration-200
            opacity-70 hover:opacity-100
          ">
                            <CloseOutlined className="text-xs"/>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};
