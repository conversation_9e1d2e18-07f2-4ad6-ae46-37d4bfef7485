import React, {useCallback, useRef} from 'react';
import {useDrop} from 'react-dnd';
import {DraggableTab} from './DraggableTab';
import {DragItem, DragTypes, DropResult, TabItem} from './types';
import {usePdfStore} from "@/store/pdf-store.ts";

interface DroppableTabContainerProps {
    tabs: TabItem[];
    windowId: string;
    activeTabKey: string;
    dragTabWindowId: string;
    onTabClick: (key: string) => void;
    onTabClose: (key: string) => void;
    onTabsReorder: (tabs: TabItem[]) => void;
    onTabDragEnd: (item: DragItem, result: DropResult | null, monitor: any) => void;
    className?: string;
}

export const DroppableTabContainer: React.FC<DroppableTabContainerProps> = ({
                                                                                tabs,
                                                                                windowId,
                                                                                activeTabKey,
                                                                                dragTabWindowId,
                                                                                onTabClick,
                                                                                onTabClose,
                                                                                onTabsReorder,
                                                                                onTabDragEnd,
                                                                                className,
                                                                            }) => {
    const {setActiveAid, updateWindow, addWindowsContainer} = usePdfStore((state) => ({
        setActiveAid: state.setActiveAid,
        updateWindow: state.updateWindow,
        addWindowsContainer: state.addWindowsContainer
    }))
    const containerRef = useRef<HTMLDivElement>(null);
    const [{isOver, canDrop}, drop] = useDrop({
        accept: DragTypes.TAB,
        drop: (item: DragItem): DropResult => {
            // 检查是否是从窗口拖拽回来的标签
            const isFromWindow = !tabs.some(tab => tab.key === item.id);
            console.log('DroppableTabContainer drop', {item, isFromWindow, tabs});

            if (isFromWindow) {
                const result = {
                    type: 'merge' as const,
                    targetContainer: windowId
                };
                console.log('DroppableTabContainer returning merge result', result);
                return result;
            } else {
                const result = {
                    type: 'reorder' as const,
                };
                console.log('DroppableTabContainer returning reorder result', result);
                return result;
            }
        },
        collect: (monitor) => ({
            isOver: monitor.isOver(),
            canDrop: monitor.canDrop(),
        }),
    });

    const moveTab = useCallback((dragItem: TabItem, hoverItem: TabItem, windowId: string) => {
        const dragIndex = tabs.findIndex(tab => tab.key === dragItem.key);
        const hoverIndex = tabs.findIndex(tab => tab.key === hoverItem.key);

        const newTabs = [...tabs];
        if (dragIndex !== -1) {
            newTabs.splice(dragIndex, 1);
        }
        newTabs.splice(hoverIndex, 0, dragItem);

        onTabsReorder(newTabs);
        if (windowId === 'main') {
            setActiveAid(dragItem.key)
        } else {
            updateWindow(windowId, {activeTabKey: dragItem.key})
        }
    }, [tabs, onTabsReorder]);

    // 合并refs
    const setRefs = useCallback((node: HTMLDivElement) => {
        containerRef.current = node;
        drop(node);
        addWindowsContainer(windowId, containerRef)
    }, [drop]);
    console.log('DroppableTabContainer isDraggingTab', dragTabWindowId === windowId)

    return (
        <div
            ref={setRefs}
            className={`
        flex items-center bg-gray-50 border-b border-gray-200
        min-h-[40px] overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300
        transition-all duration-200 ease-in-out
        ${isOver && canDrop ? 'bg-blue-50 border-blue-300 border-b-2 shadow-sm' : ''}
        ${className}
        ${dragTabWindowId === windowId ? "hidden" : ''}
      `}
        >
            {/* 标签列表 */}
            <div className="flex items-center min-w-0 flex-1">
                {tabs.map((tab, index) => (
                    <div key={tab.key} className="group">
                        <DraggableTab
                            tab={tab}
                            index={index}
                            windowId={windowId}
                            isActive={tab.key === activeTabKey}
                            onTabClick={onTabClick}
                            onTabClose={onTabClose}
                            onTabMove={moveTab}
                            onTabDragEnd={onTabDragEnd}
                            containerRef={containerRef}
                        />
                    </div>
                ))}
            </div>
        </div>
    );
};
